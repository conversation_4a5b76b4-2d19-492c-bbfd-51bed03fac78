# GitHub Secrets Setup for Crebost Deployment

## 📋 Required Secrets

Untuk menjalankan CI/CD deployment ke Cloudflare, Anda perlu mengatur secrets berikut di GitHub repository:

### 🔐 Cloudflare Secrets

1. **CLOUDFLARE_API_TOKEN**
   - **Deskripsi**: API token untuk mengakses Cloudflare API
   - **Cara mendapatkan**:
     1. <PERSON><PERSON> ke [Cloudflare Dashboard](https://dash.cloudflare.com)
     2. Pergi ke "My Profile" → "API Tokens"
     3. Klik "Create Token"
     4. Pilih "Custom token" dengan permissions:
        - Zone:Zone:Read
        - Zone:DNS:Edit
        - Account:Cloudflare Pages:Edit
        - Account:D1:Edit
        - Account:Workers KV Storage:Edit
        - Account:Workers Scripts:Edit
        - Account:R2:Edit
     5. Copy token yang dihasilkan

2. **CLOUDFLARE_ACCOUNT_ID**
   - **Deskripsi**: Account ID Cloudflare Anda
   - **<PERSON> mendapatkan**:
     1. <PERSON>gin ke Cloudflare Dashboard
     2. <PERSON>lih domain Anda
     3. Account ID terlihat di sidebar kanan

### 🔑 Authentication Secrets

3. **BETTER_AUTH_SECRET**
   - **Deskripsi**: Secret key untuk BetterAuth
   - **Value**: `L90cbYFfrXn3Yl1TewISaJLU2bFsSNWN`
   - **Note**: Sudah dikonfigurasi dalam project

### 💳 Payment Secrets

4. **MIDTRANS_SERVER_KEY**
   - **Deskripsi**: Server key dari Midtrans untuk payment processing
   - **Cara mendapatkan**:
     1. Login ke [Midtrans Dashboard](https://dashboard.midtrans.com)
     2. Pergi ke Settings → Access Keys
     3. Copy Server Key (Sandbox atau Production)

5. **MIDTRANS_CLIENT_KEY**
   - **Deskripsi**: Client key dari Midtrans untuk frontend
   - **Cara mendapatkan**: Sama seperti server key, tapi ambil Client Key

### 🗄️ Database Secrets

6. **DATABASE_URL**
   - **Deskripsi**: Connection string untuk D1 database
   - **Format**: `file:./dev.db` (untuk development) atau D1 connection string
   - **Note**: Akan dikonfigurasi setelah D1 database dibuat

## 🛠️ Cara Setup Secrets di GitHub

### Method 1: Via GitHub Web Interface

1. Pergi ke repository GitHub: `https://github.com/rendoarsandi/crebost`
2. Klik tab **Settings**
3. Di sidebar kiri, klik **Secrets and variables** → **Actions**
4. Klik **New repository secret**
5. Masukkan nama secret dan value
6. Klik **Add secret**
7. Ulangi untuk semua secrets yang diperlukan

### Method 2: Via GitHub CLI

```bash
# Install GitHub CLI jika belum ada
# https://cli.github.com/

# Login ke GitHub
gh auth login

# Set secrets
gh secret set CLOUDFLARE_API_TOKEN --body "your-cloudflare-api-token"
gh secret set CLOUDFLARE_ACCOUNT_ID --body "your-cloudflare-account-id"
gh secret set BETTER_AUTH_SECRET --body "L90cbYFfrXn3Yl1TewISaJLU2bFsSNWN"
gh secret set MIDTRANS_SERVER_KEY --body "your-midtrans-server-key"
gh secret set MIDTRANS_CLIENT_KEY --body "your-midtrans-client-key"
gh secret set DATABASE_URL --body "your-database-url"
```

## 📝 Checklist Setup

- [ ] **CLOUDFLARE_API_TOKEN** - API token dengan permissions yang benar
- [ ] **CLOUDFLARE_ACCOUNT_ID** - Account ID dari Cloudflare dashboard
- [ ] **BETTER_AUTH_SECRET** - Secret untuk BetterAuth (sudah ada)
- [ ] **MIDTRANS_SERVER_KEY** - Server key dari Midtrans
- [ ] **MIDTRANS_CLIENT_KEY** - Client key dari Midtrans
- [ ] **DATABASE_URL** - Connection string untuk database

## 🔍 Verifikasi Setup

Setelah semua secrets dikonfigurasi, Anda bisa:

1. **Test GitHub Actions**:
   ```bash
   git add .
   git commit -m "Setup GitHub secrets"
   git push origin main
   ```

2. **Monitor Deployment**:
   - Pergi ke tab **Actions** di GitHub repository
   - Lihat workflow "Deploy Crebost to Cloudflare Pages"
   - Monitor progress setiap job

3. **Check Deployment Status**:
   - Landing: https://landing.crebost.com
   - Auth: https://auth.crebost.com
   - Dashboard: https://dashboard.crebost.com
   - Admin: https://admin.crebost.com

## 🚨 Security Notes

1. **Jangan commit secrets** ke repository
2. **Gunakan environment-specific secrets** untuk staging vs production
3. **Rotate secrets secara berkala** untuk keamanan
4. **Monitor access logs** di Cloudflare dan Midtrans
5. **Gunakan least privilege principle** untuk API tokens

## 🔧 Troubleshooting

### Common Issues

1. **Invalid API Token**
   - Pastikan token memiliki permissions yang benar
   - Check expiration date
   - Regenerate jika perlu

2. **Account ID Not Found**
   - Pastikan menggunakan Account ID, bukan Zone ID
   - Check di Cloudflare dashboard sidebar

3. **Midtrans Keys Invalid**
   - Pastikan menggunakan keys yang benar (Sandbox vs Production)
   - Check di Midtrans dashboard

4. **Database Connection Failed**
   - Pastikan D1 database sudah dibuat
   - Check connection string format

### Debug Commands

```bash
# Check if secrets are set
gh secret list

# Test Cloudflare API
curl -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
  -H "Authorization: Bearer YOUR_API_TOKEN"

# Test Midtrans API
curl -X GET "https://api.sandbox.midtrans.com/v2/ping" \
  -H "Authorization: Basic $(echo -n 'YOUR_SERVER_KEY:' | base64)"
```

## 📞 Support

Jika mengalami masalah:

1. **GitHub Actions Logs**: Check detailed logs di tab Actions
2. **Cloudflare Dashboard**: Monitor deployment status
3. **Midtrans Dashboard**: Check API key status
4. **Documentation**: 
   - [Cloudflare API](https://developers.cloudflare.com/api/)
   - [Midtrans API](https://docs.midtrans.com/)
   - [GitHub Actions](https://docs.github.com/en/actions)

---

**Last Updated**: 2025-06-19
**Status**: Ready for Configuration
**Next Action**: Setup secrets dan trigger deployment
