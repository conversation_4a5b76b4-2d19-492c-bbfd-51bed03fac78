name: Deploy Crebost to Cloudflare Pages

on:
  push:
    branches: [main, production]
  pull_request:
    branches: [main]
  workflow_dispatch:

env:
  NODE_VERSION: '18'

jobs:
  # Build and test job
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build shared packages
        run: |
          npm run build --workspace=@crebost/shared
          npm run build --workspace=@crebost/database
          npm run build --workspace=@crebost/ui

      - name: Run tests
        run: npm test

      - name: Run linting
        run: npm run lint

      - name: Type check
        run: npm run type-check

  # Deploy to staging (on pull requests)
  deploy-staging:
    if: github.event_name == 'pull_request'
    needs: build-and-test
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build applications
        run: |
          npm run build --workspace=@crebost/shared
          npm run build --workspace=@crebost/database
          npm run build --workspace=@crebost/ui
          npm run build --workspace=@crebost/landing
          npm run build --workspace=@crebost/auth
          npm run build --workspace=@crebost/dashboard
          npm run build --workspace=@crebost/admin

      - name: Deploy to Cloudflare Pages (Staging)
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: crebost-landing-staging
          directory: apps/landing/dist
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}

  # Deploy to production (on main branch)
  deploy-production:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: build-and-test
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build shared packages
        run: |
          npm run build --workspace=@crebost/shared
          npm run build --workspace=@crebost/database
          npm run build --workspace=@crebost/ui

      - name: Build Landing Page
        run: npm run build --workspace=@crebost/landing
        env:
          NEXT_PUBLIC_AUTH_URL: https://auth.crebost.com
          NEXT_PUBLIC_DASHBOARD_URL: https://dashboard.crebost.com
          NEXT_PUBLIC_ADMIN_URL: https://admin.crebost.com

      - name: Deploy Landing Page
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: crebost-landing
          directory: apps/landing/dist
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Auth Service
        run: npm run build --workspace=@crebost/auth
        env:
          BETTER_AUTH_URL: https://auth.crebost.com
          BETTER_AUTH_SECRET: ${{ secrets.BETTER_AUTH_SECRET }}
          NEXT_PUBLIC_LANDING_URL: https://landing.crebost.com
          NEXT_PUBLIC_DASHBOARD_URL: https://dashboard.crebost.com
          NEXT_PUBLIC_ADMIN_URL: https://admin.crebost.com

      - name: Deploy Auth Service
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: crebost-auth
          directory: apps/auth/.next
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Dashboard
        run: npm run build --workspace=@crebost/dashboard
        env:
          NEXT_PUBLIC_AUTH_URL: https://auth.crebost.com
          NEXT_PUBLIC_LANDING_URL: https://landing.crebost.com
          NEXT_PUBLIC_ADMIN_URL: https://admin.crebost.com
          NEXT_PUBLIC_MIDTRANS_CLIENT_KEY: ${{ secrets.MIDTRANS_CLIENT_KEY }}

      - name: Deploy Dashboard
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: crebost-dashboard
          directory: apps/dashboard/.next
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Admin Panel
        run: npm run build --workspace=@crebost/admin
        env:
          NEXT_PUBLIC_AUTH_URL: https://auth.crebost.com
          NEXT_PUBLIC_LANDING_URL: https://landing.crebost.com
          NEXT_PUBLIC_DASHBOARD_URL: https://dashboard.crebost.com

      - name: Deploy Admin Panel
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: crebost-admin
          directory: apps/admin/.next
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}

      - name: Run Database Migrations
        run: |
          npx prisma generate
          npx prisma db push
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Notify Deployment Success
        if: success()
        run: |
          echo "🎉 Deployment successful!"
          echo "Landing: https://landing.crebost.com"
          echo "Auth: https://auth.crebost.com"
          echo "Dashboard: https://dashboard.crebost.com"
          echo "Admin: https://admin.crebost.com"

      - name: Notify Deployment Failure
        if: failure()
        run: |
          echo "❌ Deployment failed!"
          echo "Please check the logs for more information."

  # Health check job
  health-check:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: deploy-production
    runs-on: ubuntu-latest
    steps:
      - name: Wait for deployment
        run: sleep 60

      - name: Health check - Landing Page
        run: |
          curl -f https://landing.crebost.com || exit 1
          echo "✅ Landing page is healthy"

      - name: Health check - Auth Service
        run: |
          curl -f https://auth.crebost.com/api/health || exit 1
          echo "✅ Auth service is healthy"

      - name: Health check - Dashboard
        run: |
          curl -f https://dashboard.crebost.com/api/health || exit 1
          echo "✅ Dashboard is healthy"

      - name: Health check - Admin Panel
        run: |
          curl -f https://admin.crebost.com/api/health || exit 1
          echo "✅ Admin panel is healthy"

      - name: All services healthy
        run: echo "🎉 All services are healthy and running!"
