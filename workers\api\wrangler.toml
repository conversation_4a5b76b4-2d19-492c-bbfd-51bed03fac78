name = "crebost-api"
main = "index.js"
compatibility_date = "2024-01-01"

# Environment variables
[env.production.vars]
NODE_ENV = "production"
CORS_ORIGINS = "https://landing.crebost.com,https://auth.crebost.com,https://dashboard.crebost.com,https://admin.crebost.com"

# D1 Database bindings
[[env.production.d1_databases]]
binding = "DB"
database_name = "crebost-production"
database_id = "your-d1-database-id"

# KV namespace bindings
[[env.production.kv_namespaces]]
binding = "SESSIONS"
id = "your-sessions-kv-id"

[[env.production.kv_namespaces]]
binding = "CACHE"
id = "your-cache-kv-id"

[[env.production.kv_namespaces]]
binding = "ANALYTICS"
id = "your-analytics-kv-id"

# R2 bucket bindings
[[env.production.r2_buckets]]
binding = "UPLOADS"
bucket_name = "crebost-uploads"

[[env.production.r2_buckets]]
binding = "ASSETS"
bucket_name = "crebost-static-assets"

# Custom domains
[env.production.routes]
pattern = "api.crebost.com/*"
zone_name = "crebost.com"
