name = "crebost-api"
main = "index.js"
compatibility_date = "2024-01-01"

# Environment variables
[vars]
NODE_ENV = "production"
CORS_ORIGINS = "https://landing.crebost.com,https://auth.crebost.com,https://dashboard.crebost.com,https://admin.crebost.com"
BETTER_AUTH_SECRET = "L90cbYFfrXn3Yl1TewISaJLU2bFsSNWN"
BETTER_AUTH_URL = "https://auth.crebost.com"

# D1 Database bindings
[[d1_databases]]
binding = "DB"
database_name = "crebost-production"
database_id = "23bed93f-255c-4394-95d8-3408fd23e3e5"

# KV namespace bindings (commented out for initial deployment)
# [[kv_namespaces]]
# binding = "SESSIONS"
# id = "4ba509d0217e4fa3878c7b9df162ae79"

# [[kv_namespaces]]
# binding = "CACHE"
# id = "11ac1ff7bee34d709cb2d155600a17150"

# [[kv_namespaces]]
# binding = "ANALYTICS"
# id = "647612dcd178469bbf1b2800e4cb3451"

# R2 bucket bindings
[[r2_buckets]]
binding = "UPLOADS"
bucket_name = "crebost-uploads"

[[r2_buckets]]
binding = "ASSETS"
bucket_name = "crebost-static-assets"
