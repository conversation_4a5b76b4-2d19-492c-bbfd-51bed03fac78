name = "crebost-auth"
compatibility_date = "2024-01-15"
pages_build_output_dir = ".next"

[env.production]
name = "crebost-auth"
route = { pattern = "auth.crebost.com/*", zone_name = "crebost.com" }

[env.production.vars]
NODE_ENV = "production"
NEXTAUTH_URL = "https://auth.crebost.com"
NEXT_PUBLIC_LANDING_URL = "https://landing.crebost.com"
NEXT_PUBLIC_DASHBOARD_URL = "https://dashboard.crebost.com"
NEXT_PUBLIC_ADMIN_URL = "https://admin.crebost.com"

[[env.production.d1_databases]]
binding = "DB"
database_name = "crebost-production"
database_id = "23bed93f-2555c-4394-95d8-3408fd23e3e5"
migrations_dir = "../../packages/database/migrations"

[[env.production.kv_namespaces]]
binding = "SESSIONS"
id = "4ba509d0217e4fa3878c77b9df162ae79"

[[env.production.kv_namespaces]]
binding = "CACHE"
id = "11ac1ff7bee34d709cb2dd15600a17150"

[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

[build.upload]
format = "modules"
