name = "crebost-webhooks"
main = "index.js"
compatibility_date = "2024-01-01"

# Environment variables
[env.production.vars]
NODE_ENV = "production"

# D1 Database bindings
[[env.production.d1_databases]]
binding = "DB"
database_name = "crebost-production"
database_id = "your-d1-database-id"

# KV namespace bindings
[[env.production.kv_namespaces]]
binding = "CACHE"
id = "your-cache-kv-id"

# Custom domains
[env.production.routes]
pattern = "webhooks.crebost.com/*"
zone_name = "crebost.com"
