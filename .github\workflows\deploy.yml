name: Deploy to Cloudflare

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'

jobs:
  deploy-landing:
    name: Deploy Landing Page
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build Landing Page
        run: |
          cd apps/landing
          npm run build

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: crebost-landing
          directory: apps/landing/out
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}

  deploy-auth:
    name: Deploy Auth Service
    runs-on: ubuntu-latest
    needs: [deploy-landing]
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build shared packages
        run: |
          npm run build --workspace=@crebost/shared
          npm run build --workspace=@crebost/database
          npm run build --workspace=@crebost/ui

      - name: Build Auth Service
        run: |
          cd apps/auth
          npm run build
        env:
          NODE_ENV: production
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Deploy Auth to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: crebost-auth
          directory: apps/auth/.next
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}

  deploy-dashboard:
    name: Deploy Dashboard
    runs-on: ubuntu-latest
    needs: [deploy-auth]
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build shared packages
        run: |
          npm run build --workspace=@crebost/shared
          npm run build --workspace=@crebost/database
          npm run build --workspace=@crebost/ui

      - name: Build Dashboard
        run: |
          cd apps/dashboard
          npm run build
        env:
          NODE_ENV: production
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Deploy Dashboard to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: crebost-dashboard
          directory: apps/dashboard/.next
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}

  deploy-admin:
    name: Deploy Admin Panel
    runs-on: ubuntu-latest
    needs: [deploy-dashboard]
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build shared packages
        run: |
          npm run build --workspace=@crebost/shared
          npm run build --workspace=@crebost/database
          npm run build --workspace=@crebost/ui

      - name: Build Admin Panel
        run: |
          cd apps/admin
          npm run build
        env:
          NODE_ENV: production
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Deploy Admin to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: crebost-admin
          directory: apps/admin/.next
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}

  database-migration:
    name: Database Migration
    runs-on: ubuntu-latest
    needs: [deploy-admin]
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Wrangler
        run: npm install -g wrangler

      - name: Run Database Migration
        run: |
          cd packages/database
          npx prisma generate
          wrangler d1 execute crebost-production --file=./prisma/migrations/schema.sql --remote
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
